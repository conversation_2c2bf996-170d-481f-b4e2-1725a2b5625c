import json
import logging
import os
import pathlib
import sys
import threading
import time
import traceback
from typing import Any, cast

import orjson
import pandas as pd
import utils_aws
import utils_general
from aws_lambda_typing import context as ctx
from aws_lambda_typing import events

from synthetic_price_calc.prep_data import prep_data_lambda
from synthetic_price_calc.process_data import process_data
from synthetic_price_calc.retrieve_data import retrieve_data
from synthetic_price_calc.typings import (
    DataSetModel,
    PreviousResults,
    S3Details,
    SyntheticPriceCalc,
)
from synthetic_price_calc.utils import (
    get_data_set_name,
    put_s3_object,
    validate_data_sets,
)

utils_general.setup_python_logger(os.getenv("LOG_LEVEL", logging.INFO))


def seppuku(timeout: int) -> None:
    time.sleep(timeout)
    os.kill(os.getpid(), 9)


def lambda_handler(
    event: events.APIGatewayProxyEventV1, context: ctx.Context
) -> Any:
    input = utils_general.json_loads(event["body"])

    try:
        open_sockets = utils_general.socket_fds()
        logging.info(f"START: {len(open_sockets)} open sockets")

        calc: SyntheticPriceCalc = input["calc"]
        calc_args = calc["args"]
        data_sets: list[DataSetModel] = [
            DataSetModel(**ds.__dict__ if isinstance(ds, DataSetModel) else ds)
            for ds in calc_args["data_sets"]
        ]

        validate_data_sets(data_sets)

        periods = int(calc_args["frequency"]["periods"])
        interval = calc_args["frequency"]["interval"]
        input_version = calc.get("input_version", "")
        frequency = f"{periods}{utils_general.INTERVAL_TO_LETTER[interval]}"
        start, end = utils_aws.get_calc_date_range(calc_args["date_range"])
        debug = bool(calc_args.get("debug", False))
        consistent_read = cast(bool, calc_args.get("consistent_read", False))
        smooth = calc_args.get("smooth", False)
        previous_result = cast(
            PreviousResults,
            (
                {"df": calc_args["previous_result"]}
                if "previous_result" in calc_args
                else {"df": None}
            ),
        )
        calc_args["previous_result"] = (
            None  # avoid issue on output json serialisation
        )
        include_result_response = calc_args.get(
            "include_result_response", False
        )

        do_store = calc["output_options"]["do_store"]
        output_type = calc["output_options"]["type"]
        output_version = calc["output_options"].get("version", "")
        if not (do_store and output_type == "csv"):
            raise ValueError(
                "Output options must include 'do_store' and 'type' must be 'csv'"
            )

        version_frequency = (
            f"{output_version}-{frequency}" if output_version else frequency
        )
        lqn_parts = [
            f"{data_set.target_exchange}+{data_set.reference_exchange}."
            + f"{data_set.target_asset}+{data_set.reference_asset}."
            + "+".join(data_set.models)
            for data_set in data_sets
        ]
        lqn = f"{version_frequency}_" + "_".join(lqn_parts)

        s3_details = S3Details(
            bucket=do_store["s3_bucket"],
            obj_prefix=cast(str, do_store.get("s3_object_prefix", "")),
            obj_suffix=cast(str, do_store.get("s3_object_suffix", "")),
            data_sets=_data_sets_to_s3_prefix(data_sets),
            frequency=frequency,
            start=start,
            end=end,
        )

        time_of_start = time.time()
        fetch_lookback_data = previous_result["df"] is None
        retrieved_data = retrieve_data(
            data_sets=data_sets,
            frequency=frequency,
            interval=interval,
            start=start,
            end=end,
            version=input_version or output_version,
            consistent_read=consistent_read,
            fetch_lookback_data=fetch_lookback_data,
        )
        logging.info(
            f"{lqn} Data retrieval took {round(time.time() - time_of_start)}s"
        )

        time_of_start = time.time()
        data = prep_data_lambda(
            retrieved_data=retrieved_data,
            data_sets=data_sets,
        )
        logging.info(f"Data prep took {round(time.time() - time_of_start)}s")

        if not data:
            raise ValueError(f"{lqn} prepped data was empty, {start=}, {end=}")

        time_of_start = time.time()
        processed_results = process_data(
            chunk=data,
            smooth=smooth,
            freq=frequency,
            version=output_version,
            s3_details=s3_details,
            debug=debug,
        )
        logging.info(
            f"{lqn} Data processing took {round(time.time() - time_of_start)}s"
        )
        results = processed_results["calc_output"]
        lookback_result = processed_results["lookback_result"]

        if results:
            df = pd.DataFrame(results)
            if input_version:
                df["qualified_name"] = df["qualified_name"].str.replace(
                    input_version, output_version
                )

            df.sort_values(by=["timestamp", "qualified_name"], inplace=True)
            s3_obj_key = put_s3_object(s3_details, df.to_csv(index=False))
            destination = f"s3://{s3_details.bucket}/{s3_obj_key}"
        else:
            raise RuntimeError(
                f"Failed to calculate synthetic params. Empty response for: {s3_details.data_sets}"
            )

        if len(open_sockets) > 700:
            timeout = 2
            logging.info(
                f"Likely socket leak: there are {len(open_sockets)} open. Killing process in {timeout}s to avoid reaching lambda limit of 1024.",
            )
            threading.Thread(target=seppuku, args=(timeout,)).start()

        # todo: where should we merge the lookback result?
        # Including results as separate param to avoid json
        # serialisation (which doesn't work on DataFrames)
        if include_result_response and smooth:
            if lookback_result is None:
                raise ValueError("lookback_result is None")

            # if LOOKBACK_COLUMN_NAME in result.columns:
            #     result = result[~result[LOOKBACK_COLUMN_NAME].eq(True)]

            addtl_response_params = {"result_df": (lookback_result)}
        if include_result_response:
            addtl_response_params = {"result_df": None}
        else:
            addtl_response_params = {}

        return {
            "statusCode": 200,
            "headers": utils_aws.lambda_response_headers("GET"),
            "body": orjson.dumps(
                {
                    "input": input,
                    "output": {
                        "status": 0,
                        "msg": "Success",
                        "results": "",
                        "storage_destination": destination,
                    },
                },
            ).decode("utf-8"),
            **addtl_response_params,
        }
    except Exception:
        logging.exception(f"Error while processing event={event}")
        return {
            "statusCode": 500,
            "headers": utils_aws.lambda_response_headers("GET"),
            "body": orjson.dumps(
                {
                    "input": input,
                    "output": {
                        "status": 1,
                        "msg": traceback.format_exc(),
                        "results": "",
                    },
                }
            ).decode("utf-8"),
        }


def _data_sets_to_s3_prefix(data_sets: list[DataSetModel]) -> str:
    res = "_".join([get_data_set_name(ds) for ds in data_sets])
    return res


if __name__ == "__main__":
    event = cast(
        events.APIGatewayProxyEventV1,
        json.load(
            open(f"{pathlib.Path(__file__).parent.resolve()}/event.json")
        ),
    )
    context = cast(ctx.Context, None)
    lambda_handler(event, context)
    sys.exit()
