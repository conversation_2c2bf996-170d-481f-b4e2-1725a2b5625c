from collections.abc import Callable
from pathlib import Path
from typing import Any, cast

import pytest

from synthetic_price_calc.typings import DataSet<PERSON><PERSON>, Snapshot, SpotFromManager

from .helpers import load_json, set_snapshot_path_to_parent


@pytest.fixture
def snapshot(snapshot: Any) -> Any:
    return set_snapshot_path_to_parent(snapshot)


@pytest.fixture
def assemble_process_chunk_helper_snapshot() -> Callable[[str], Snapshot]:
    def _assemble_snapshot(test_case: str) -> Snapshot:
        data_dir = Path(__file__).parent / "fixtures" / test_case

        other_fields_path = data_dir / "base_snapshot.json"
        other_fields = load_json(other_fields_path)

        target_spot_path = data_dir / "spot" / "target_asset.json"

        target_spot_data = cast(
            list[SpotFromManager], load_json(target_spot_path)
        )

        reference_spot_path = data_dir / "spot" / "reference_asset.json"
        reference_spot_data = cast(
            list[SpotFromManager], load_json(reference_spot_path)
        )

        # Load data_snap
        data_snap_raw: list[dict[str, Any]] = load_json(
            data_dir / "data_snap.json"
        )
        data_snap = [DataSetSnap(**data) for data in data_snap_raw]

        snapshot_data = Snapshot(
            timestamp=other_fields["timestamp"],
            target_asset=other_fields["target_asset"],
            target_exchange=other_fields["target_exchange"],
            reference_asset=other_fields["reference_asset"],
            reference_exchange=other_fields["reference_exchange"],
            target_asset_spot_data=target_spot_data,
            reference_asset_spot_data=reference_spot_data,
            data_snap=data_snap,
        )

        return snapshot_data

    return _assemble_snapshot


@pytest.fixture(params=["process_chunk_helper"])
def process_chunk_helper_snapshot(
    request: pytest.FixtureRequest,
    assemble_process_chunk_helper_snapshot: Callable[[str], Snapshot],
) -> Snapshot:
    return assemble_process_chunk_helper_snapshot(request.param)
