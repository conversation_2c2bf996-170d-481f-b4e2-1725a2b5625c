import logging
from collections import defaultdict
from typing import Any, cast

import pandas as pd
import utils_general
from block_stream.typings import InstrumentsDetailsMap
from utils_general import (
    ConversionSpotManager,
    SpotInstrumentDetails,
)

from .config import (
    HISTORIC_SPOT_DATA_CONFIG,
    MAX_TENOR_CUTOFF,
    SPOT_DATA_HISTORICAL_TIMEDELTA,
)
from .type_guards import (
    is_listed_params_bundle,
    is_listed_tenor_params,
    is_matching_model_params_bundle,
    is_raw_future,
    is_raw_spot,
)
from .typings import (
    DataQueryResult,
    DataSetModel,
    DataSetSnap,
    FutureQueryResult,
    ListedTenorParams,
    ProcessDataSnapshot,
    RetrievedData,
    Snapshot,
    SnapshotData,
    SpotFromManager,
)
from .utils import extract_future_details


def prep_data_lambda(
    retrieved_data: RetrievedData,
    data_sets: list[DataSetModel],
    **kwargs: Any,
) -> ProcessDataSnapshot:

    # we need to pass an object containing both the raw_data and the lookback data
    # is passed into prep_data so that lookback functionality is retained in both
    # scheduled and live mode.

    prepped_data = prep_data(
        raw_data=retrieved_data.raw_data,
        instruments=retrieved_data.instruments_details,
        data_sets=data_sets,
        **kwargs,
    )

    # Attach lookback data to prepped data. process_data expects a lookback to be passed in both
    # the scheduled and live system. We attach the lookback here so that the function signature
    # of prep_data doesnt change and disrupt the live system.
    lookback_data = retrieved_data.lookback_data
    if lookback_data is not None:
        prepped_data.previous_results = {"df": pd.DataFrame(lookback_data)}

    return prepped_data


def prep_data(
    raw_data: list[DataQueryResult],
    instruments: InstrumentsDetailsMap,
    data_sets: list[DataSetModel],
    snapshot_ts: int | None = None,
    **kwargs: Any,  # for compatibility with blockstream
) -> ProcessDataSnapshot:

    spot_instruments = cast(
        list[SpotInstrumentDetails],
        [instr for key, instr in instruments.items() if "spot" in key],
    )
    spot_manager = ConversionSpotManager(spot_instruments=spot_instruments)

    calc_snapshots: dict[int, dict[str, SnapshotData]] = (
        utils_general.nested_dict()
    )

    for r in raw_data:
        # update the timestamp-matched snapshot with this data result
        timestamp = r["timestamp"]

        if is_raw_spot(r):
            try:
                spot_manager.add_spot_data(r)

            except Exception as _err:
                logging.error(f"Failed to add spot data, Error={_err}")

        elif snapshot_ts and (snapshot_ts != timestamp):
            continue

        elif is_listed_params_bundle(r):
            _, qn_tokens = utils_general.get_qfn_and_version(
                r["qualified_name"]
            )
            for param in r["params"]:
                if param["expiry"] * 365 > MAX_TENOR_CUTOFF:
                    """
                    The tenor_days we shift the function used when calculating moments:
                        "pxs_df["px"] / pxs_df["px"].shift(periods=int(tenor_days) or 1)"
                    cannot be greater than the number of days we fetch historical spot prices for.
                    Longer tenors require a longer history of spot data to sensibly construct the spot returns.
                    e.g  we need a minimum of 6 months of spot data for 90d.
                    """
                    continue
                params_expiry = utils_general.convert_year_expiry_to_iso_stamp(
                    param["expiry"], param["timestamp"]
                )
                if params_expiry not in calc_snapshots[timestamp]:
                    calc_snapshots[timestamp][params_expiry] = {
                        "params": [],
                        "futures": [],
                    }

                param["timestamp"] = r["timestamp"]

                # add more identifying info to the expiry object
                param["base"] = qn_tokens[2]
                param["exchange"] = qn_tokens[0]
                calc_snapshots[timestamp][params_expiry]["params"].append(param)

        elif is_raw_future(r):

            _, future_exp = extract_future_details(r["qualified_name"])

            if future_exp not in calc_snapshots[timestamp]:
                calc_snapshots[timestamp][future_exp] = {
                    "params": [],
                    "futures": [],
                }
            calc_snapshots[timestamp][future_exp]["futures"].append(r)

        else:
            raise ValueError(f"Unrecognised record found: {r=}")

    snapshots = []
    for data_set in data_sets:
        timestamp_results_map: dict[int, list[Snapshot]] = {}

        for timestamp, expiry_to_snapshot in calc_snapshots.items():

            try:
                latest_spot_price, message = spot_manager.get_spot(
                    data_set.target_asset, "USD", timestamp
                )
                if not latest_spot_price:
                    raise ValueError(f"Latest spot price not found. {message=}")

            except Exception:
                logging.exception(
                    f"Could not find latest spot price for {data_set.target_asset=}, iso_stamp={utils_general.to_iso(timestamp)}. Skipping snapshot"
                )
                continue

            try:
                grouped_results = _group_model_params_and_futures_for_data_set(
                    snapshot_data=expiry_to_snapshot,
                    spot=latest_spot_price,
                    timestamp=timestamp,
                    reference_exchange=data_set.reference_exchange,
                    reference_asset=data_set.reference_asset,
                    target_exchange=data_set.target_exchange,
                    target_asset=data_set.target_asset,
                    models=data_set.models,
                )
            except Exception:
                logging.exception(
                    f"No valid data found for {data_set.target_asset=}, {data_set.target_exchange=}, {data_set.models=}, "
                    f"at timestamp {utils_general.to_iso(timestamp)}"
                )
                continue

            target_asset_spot_data = (
                _get_historical_snapshots_from_spot_manager(
                    spot_manager, data_set.target_asset, "USD", timestamp
                )
            )
            reference_asset_spot_data = (
                _get_historical_snapshots_from_spot_manager(
                    spot_manager, data_set.reference_asset, "USD", timestamp
                )
            )

            if target_asset_spot_data and reference_asset_spot_data:
                snapshot = Snapshot(
                    timestamp=timestamp,
                    data_snap=grouped_results,
                    target_asset=data_set.target_asset,
                    target_exchange=data_set.target_exchange,
                    target_asset_spot_data=target_asset_spot_data,
                    reference_asset=data_set.reference_asset,
                    reference_exchange=data_set.reference_exchange,
                    reference_asset_spot_data=reference_asset_spot_data,
                )

                if timestamp not in timestamp_results_map:
                    timestamp_results_map[timestamp] = []
                timestamp_results_map[timestamp].append(snapshot)
            else:
                logging.error(
                    f"Error while prepping data for {utils_general.to_iso(timestamp)}, {timestamp=}, {data_set.target_asset=}, {data_set.reference_exchange=}, "
                    f"{data_set.reference_asset=}. Target asset or reference asset spot is missing. Skipping snapshot"
                )
                continue

        for _, timestamp_snapshots in timestamp_results_map.items():
            snapshots.append(timestamp_snapshots)

    return ProcessDataSnapshot(params=snapshots, previous_results={"df": None})


def _get_historical_snapshots_from_spot_manager(
    spot_manager: ConversionSpotManager,
    base: str,
    quote: str,
    timestamp: int,
) -> list[SpotFromManager]:

    timestamp_dt = utils_general.to_datetime(timestamp)
    # log the missing timestamps every half-hour to avoid excessive logging
    log_timestamps = timestamp_dt.minute % 30 == 0 and timestamp_dt.second == 0

    historic_lookback_end = utils_general.to_datetime(timestamp).replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    historical_lookback_start = (
        historic_lookback_end - SPOT_DATA_HISTORICAL_TIMEDELTA
    )

    historical_timestamps = utils_general.generate_timestamps(
        start=utils_general.to_iso(historical_lookback_start),
        end=utils_general.to_iso(historic_lookback_end),
        interval=str(HISTORIC_SPOT_DATA_CONFIG["interval"]),
        periods=int(HISTORIC_SPOT_DATA_CONFIG["periods"]),
        time_condition={},
    )

    spot_timestamps: list[SpotFromManager] = []
    missing_timestamps_map: dict[str, list[float]] = defaultdict(list)
    for timestamp in historical_timestamps:
        spot_px, message = None, None
        try:

            spot_px, message = spot_manager.get_spot(base, quote, timestamp)
            if spot_px:
                spot_timestamps.append(
                    SpotFromManager(
                        px=spot_px, message=message, timestamp=timestamp
                    )
                )
            elif message:
                missing_timestamps_map[message].append(timestamp)

        except Exception as e:
            utils_general.log_bsdebug(
                f"Missing spot price at {timestamp=}. Error: {e=}"
            )
            missing_timestamps_map["lookup_error"].append(timestamp)

    if len(spot_timestamps) != len(historical_timestamps):
        logging.warning(
            f"Missing {base=}, {quote=} historical spot timestamps. Found {len(spot_timestamps)}/{len(historical_timestamps)} timestamps"
        )
        if log_timestamps and missing_timestamps_map:
            logging.error(f"Missing Spot timestamps: {missing_timestamps_map}")

    return spot_timestamps


def _extract_exchange_asset(
    item: ListedTenorParams | FutureQueryResult,
) -> tuple[str, str]:
    if is_listed_tenor_params(item):
        exchange = item["exchange"]
        base = item["base"]
    elif is_raw_future(item):
        # Note: The below token extraction assumes the futures are synthetic and created by the forward calc.
        # This will fail for traded futures.
        qfn_parts = utils_general.get_qfn_and_version(item["qualified_name"])[1]
        exchange = qfn_parts[0]
        base = qfn_parts[2].split("_")[0]
    else:
        raise NotImplementedError(f"Encountered unknown asset type. {item=}")
    return exchange, base


def _find_unique_matching_item(
    items: list[ListedTenorParams] | list[FutureQueryResult],
    exchange: str,
    base: str,
    expiry_str: str,
    asset_type: str,
    model: str = "",
) -> ListedTenorParams | FutureQueryResult | None:
    matching_items = [
        item
        for item in items
        if _extract_exchange_asset(item) == (exchange, base)
        and (
            asset_type == "future"
            or is_matching_model_params_bundle(item, model)
        )
    ]
    if len(matching_items) > 1:
        raise AssertionError(
            f"Expected only one {asset_type} for expiry {expiry_str}, found {len(matching_items)}."
        )
    return matching_items[0] if matching_items else None


def _group_model_params_and_futures_for_data_set(
    snapshot_data: dict[str, SnapshotData],
    spot: float,
    timestamp: int,
    reference_exchange: str,
    reference_asset: str,
    target_exchange: str,
    target_asset: str,
    models: list[str],
) -> list[DataSetSnap]:
    """
    Groups model parameters and futures into datasets based on the provided filters.

    Args:
        snapshot_data (dict[str, SnapshotData]): Mapping of expiry to SnapshotData.
        spot (float): Spot price.
        timestamp (int): Timestamp of the snapshot.
        reference_exchange (str): Reference exchange to filter model parameters.
        reference_asset (str): Reference asset to filter model parameters.
        target_exchange (str): Target exchange to filter futures.
        target_asset (str): Target asset to filter futures.

    Returns:
        List[DataSetSnap]: List of grouped DataSetSnap instances.

    Raises:
        ValueError: If no model parameters are found across all expiries.
        AssertionError: If multiple model parameters or futures are found for an expiry.
    """

    results = []

    for expiry_str, data in snapshot_data.items():
        for model in models:
            try:
                model_param = _find_unique_matching_item(
                    data["params"],
                    reference_exchange,
                    reference_asset,
                    expiry_str,
                    "option",
                    model,
                )

                if not model_param:
                    logging.warning(
                        f"No modelparams found Skipping snapshot. {model=}, iso_stamp={utils_general.to_iso(timestamp)}, {expiry_str=}, {target_asset=}, {reference_asset=}"
                    )
                    continue

                model_param = cast(ListedTenorParams, model_param)

                future = _find_unique_matching_item(
                    data["futures"],
                    target_exchange,
                    target_asset,
                    expiry_str,
                    "future",
                )

                if not future:
                    logging.warning(
                        f"Encountered model parameters that have no associated futures for {model=} {utils_general.to_iso(timestamp)}, "
                        f"{timestamp=}, {expiry_str=}, {target_asset=}, {reference_asset=}"
                    )
                    continue

                future = cast(FutureQueryResult, future)

                results.append(
                    DataSetSnap(
                        params=model_param,
                        future=future,
                        spot=spot,
                        model=model,
                    )
                )
            except Exception as _err:
                logging.error(
                    f"Failed to process dataset for {model=}, {expiry_str=}, {utils_general.to_iso(timestamp)}, {timestamp=}, Error={_err}"
                )

    if not results:
        raise ValueError(
            f"No valid model parameters found in snapshot data. Skipping snapshot. iso_stamp={utils_general.to_iso(timestamp)}, {target_asset=}, {reference_asset=}"
        )

    return results
