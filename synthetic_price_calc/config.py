import multiprocessing
import os
from datetime import timedelta

import boto3
from mypy_boto3_s3 import S3Client as S3ClientSync

S3: S3ClientSync = boto3.client("s3")
S3_PREFIX = "syntheticPriceCalc"
BLOCKSTREAM_ENDPOINT = os.environ.get("BLOCKSTREAM_ENDPOINT")
# Longer tenors require a longer history of spot data
SPOT_DATA_HISTORICAL_TIMEDELTA = timedelta(
    days=int(os.environ.get("SPOT_DATA_HISTORICAL_TIMEDELTA", 180))
)
LATEST_TICK_TIME_WINDOW = timedelta(
    minutes=int(os.environ.get("LATEST_TICK_TIME_WINDOW_MINUTES", 15))
)

NUM_WORKERS = multiprocessing.cpu_count() * 2
RUN_CLEANUP_FNS_AFTER_SECS = 150
METRIC_S = int(os.environ.get("METRIC_S", 60))
HISTORIC_SPOT_DATA_CONFIG: dict[str, str | int] = {
    "periods": 24,
    "interval": "hour",
}
SSM_DATA_SET_CONFIG_PATH = "/config/synthetic_price_calc/"

LIVE_SYSTEM_START_END_DIFF = timedelta(
    minutes=int(os.environ.get("LIVE_SYSTEM_START_END_DIFF_MINUTES", 10))
)

MAX_TENOR_CUTOFF = 120

R2_ACCEPTABLE = 0.5

LOOKBACK_COLUMN_NAME = "from_lookback"
UNIX_MINUTE_NS = 60 * 1e9
UNIX_HOUR_NS = 60 * UNIX_MINUTE_NS
