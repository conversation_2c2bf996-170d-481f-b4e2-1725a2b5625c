from dataclasses import dataclass
from typing import NotRequired, TypedDict

from block_stream.typings import InstrumentsDetailsMap
from pandas import DataFrame
from pydantic import BaseModel
from utils_aws import CalcBase, DateRange, Frequency
from utils_general import DataQueryPxResult


class PxItem(TypedDict):
    timestamp: int
    px: float


class SabrParams(TypedDict):
    sabr_alpha: float
    sabr_rho: float
    sabr_volvol: float


class SviParams(TypedDict):
    svi_a: float
    svi_b: float
    svi_m: float
    svi_rho: float
    svi_sigma: float


class DataSetArgs(TypedDict):
    target_asset: str
    reference_asset: str
    reference_exchange: str
    target_exchange: str
    models: list[str]


class DataSetModel(BaseModel):
    target_asset: str
    reference_asset: str
    reference_exchange: str
    target_exchange: str
    models: list[str]


class SpotDataResult(TypedDict):
    qualified_name: str
    timestamp: int
    px: float


class BSExchangeIntermediate(TypedDict):
    px: float
    pxRate: float
    qty: float
    qtyRate: float


class BSBaseIndexQueryResult(DataQueryPxResult):
    intermediate: dict[str, BSExchangeIntermediate]


class FutureQueryResult(DataQueryPxResult):
    pass


class BaseParams(TypedDict):
    MARD: float
    R2: float
    spot: float
    expiry: float
    timestamp: int
    atm_vol: float
    forward: float
    max_strike: float
    MARD_strike: float
    askiv: list[float]
    bidiv: list[float]
    midiv: list[float]
    min_strike: float
    strikes_total: int
    strikes_inside: int
    underlying_index: str
    strikescalib: list[float]
    strikes_prefilter_total: int
    bid_ask_dev_max: list[float]
    bid_ask_dev_max_strike: list[float]
    smoothed_strikes_prefilter_total: float


class SabrParamsBundle(BaseParams, SabrParams):
    exchange: NotRequired[str]
    base: NotRequired[str]


class SviParamsBundle(BaseParams, SviParams):
    exchange: NotRequired[str]
    base: NotRequired[str]


ListedTenorParams = SabrParamsBundle | SviParamsBundle


class ListedParamsBundle(TypedDict):
    qualified_name: str
    L1_arbcheck_failed: bool  # v-00004
    runtime: str
    params: list[ListedTenorParams]
    timestamp: int


DataQueryResult = SpotDataResult | FutureQueryResult | ListedParamsBundle


class SpotFromManager(PxItem):
    message: str | None


class SabrSingleTenor(SabrParams):
    timestamp: int
    expiry: float


class SviSingleTenor(SviParams):
    timestamp: int
    expiry: float


SingleTenor = SabrSingleTenor | SviSingleTenor


class SviTenorResult(SviParams):
    qualified_name: str
    forward: float
    underlying_index: str
    spot: float
    timestamp: int
    expiry: float
    atm_vol: float
    calib_r2: float


class SabrTenorResult(SabrParams):
    qualified_name: str
    forward: float
    underlying_index: str
    spot: float
    timestamp: int
    expiry: float
    atm_vol: float


SingleTenorResult = SabrTenorResult | SviTenorResult


@dataclass
class RetrievedData:
    raw_data: list[DataQueryResult]
    instruments_details: InstrumentsDetailsMap
    lookback_data: list[SingleTenorResult] | None


class IntermediateCalcOutput(TypedDict):
    params: list[SingleTenorResult]
    model: str
    timestamp: int
    target_asset: str
    qualified_name: str
    runtime: str


class LookbackCalcOutput(TypedDict):
    qualified_name: str
    params: list[SingleTenorResult]
    runtime: str
    timestamp: int


class CalcOutput(TypedDict):
    qualified_name: str
    params: str
    runtime: str
    timestamp: int


class ProcessDataResult(TypedDict):
    calc_output: list[CalcOutput]
    lookback_result: DataFrame | None


class SnapshotData(TypedDict):
    params: list[ListedTenorParams]
    futures: list[FutureQueryResult]


class StatMoments(BaseModel):
    mean: float
    std: float
    variance: float
    skewness: float
    kurtosis: float


@dataclass
class S3Details:
    bucket: str
    obj_prefix: str
    obj_suffix: str
    data_sets: str
    frequency: str
    start: str
    end: str


class CalcArgs(TypedDict):
    data_sets: list[DataSetModel]
    frequency: Frequency
    date_range: DateRange
    debug: bool | None
    consistent_read: bool | None
    smooth: NotRequired[bool]
    previous_result: DataFrame | None
    include_result_response: bool | None


class CalcBaseWithInputVersion(CalcBase):
    input_version: NotRequired[str]


class SyntheticPriceCalc(CalcBaseWithInputVersion):
    args: CalcArgs


@dataclass
class DataSetSnap:
    spot: float
    params: ListedTenorParams
    future: FutureQueryResult
    model: str


@dataclass
class Snapshot:
    timestamp: int
    target_asset: str
    target_exchange: str
    reference_asset: str
    reference_exchange: str
    target_asset_spot_data: list[SpotFromManager]
    reference_asset_spot_data: list[SpotFromManager]
    data_snap: list[DataSetSnap]


class FailedTenorObject(TypedDict):
    failed_tenor: SingleTenor
    reference_tenor: ListedTenorParams


class PreviousResults(TypedDict):
    df: DataFrame | None


class ZscoreDict(TypedDict, total=False):
    window: int
    span_smooth: dict[str, int]
    span_zscore: dict[str, int]
    adjust: bool


class SmoothParamsDetails(ZscoreDict):
    round_to_seconds: NotRequired[int]


SmoothParamsDict = dict[str, SmoothParamsDetails]

SnapshotSlices = list[list[Snapshot]]


@dataclass
class ProcessDataSnapshot:
    params: SnapshotSlices
    previous_results: PreviousResults
    lookback_end_iso: str | None = None
