import logging
from collections import defaultdict

import numpy as np
import pandas as pd
from sklearn.metrics import r2_score  # type: ignore
from utils_aws import load_ssm_params
from utils_calc import (
    DEFAULT_SURFACE_MONEYNESS,
    SABR_RHO_BOUND,
    CalibrationQuery,
    get_domestic_rate,
    perform_svi_calibration,
    svi_var,
)

from synthetic_price_calc.typings import IntermediateCalcOutput

from .config import R2_ACCEPTABLE
from .synthetic_calc import apply_isotonic_regression_by_strike
from .typings import (
    SingleTenorResult,
)


# todo: should be a generic function to group the results
def _group_smoothed_data_for_svi_refitting(
    smoothed_results: list[IntermediateCalcOutput],
) -> dict[tuple[int, str], list[IntermediateCalcOutput]]:
    """
    Group smoothed data by timestamp and model for SVI refitting.

    Args:
        smoothed_results: List of smoothed IntermediateCalcOutput objects

    Returns:
        Dictionary grouped by (timestamp, model) tuples
    """
    grouped_for_svi: dict[tuple[int, str], list[IntermediateCalcOutput]] = (
        defaultdict(list)
    )

    for result in smoothed_results:
        # Extract model from qualified_name (format: prefix.blockscholes-syn.option.asset.model.freq.params)
        qn_parts = result["qualified_name"].split(".")
        if len(qn_parts) >= 5:
            model = qn_parts[4]  # Extract model part
            timestamp = result["timestamp"]
            grouped_for_svi[(timestamp, model)].append(result)

    return dict(grouped_for_svi)


def _convert_intermediate_results_to_model_results(
    results_group: list[IntermediateCalcOutput],
) -> dict[str, list[SingleTenorResult]]:
    """
    Convert IntermediateCalcOutput objects to model_results format for SVI refitting.

    Args:
        results_group: List of IntermediateCalcOutput objects for the same model/timestamp

    Returns:
        Dictionary in the format expected by refit_svi_to_reduce_calendar_arb
    """
    model_results: dict[str, list[SingleTenorResult]] = {"SVI": []}

    for result in results_group:
        # No JSON parsing needed - we have raw SingleTenorResult objects
        model_results["SVI"].extend(result["tenor_results"])

    return model_results


def _apply_svi_refitting_to_smoothed_data(
    smoothed_results: list[IntermediateCalcOutput],
) -> list[IntermediateCalcOutput]:
    """
    Apply SVI refitting to smoothed data, preserving the IntermediateCalcOutput format.

    Args:
        smoothed_results: List of smoothed IntermediateCalcOutput objects

    Returns:
        List of IntermediateCalcOutput objects with SVI refitting applied
    """
    # Group results by timestamp and model for SVI refitting
    grouped_for_svi = _group_smoothed_data_for_svi_refitting(smoothed_results)

    refitted_results = []

    for (timestamp, model), results_group in grouped_for_svi.items():
        if model == "SVI" and len(results_group) > 1:
            # Convert to model_results format
            model_results = _convert_intermediate_results_to_model_results(
                results_group
            )

            if model_results["SVI"]:
                # Apply SVI refitting
                try:
                    refit_svi_to_reduce_calendar_arb(model_results)

                    # Convert back to IntermediateCalcOutput format
                    updated_results = (
                        _convert_model_results_back_to_intermediate_results(
                            model_results, results_group
                        )
                    )
                    refitted_results.extend(updated_results)
                except Exception:
                    logging.exception(
                        f"Error during SVI refitting for timestamp {timestamp}"
                    )
                    # Fall back to original results if refitting fails
                    refitted_results.extend(results_group)
            else:
                refitted_results.extend(results_group)
        else:
            # Non-SVI models or single results don't need refitting
            refitted_results.extend(results_group)

    return refitted_results


def _convert_model_results_back_to_intermediate_results(
    model_results: dict[str, list[SingleTenorResult]],
    original_results_group: list[IntermediateCalcOutput],
) -> list[IntermediateCalcOutput]:
    """
    Convert refitted model_results back to IntermediateCalcOutput format.

    Args:
        model_results: Refitted model results from SVI refitting
        original_results_group: Original IntermediateCalcOutput objects to update

    Returns:
        List of IntermediateCalcOutput objects with updated parameters
    """
    updated_results = []

    for i, result in enumerate(original_results_group):
        if i < len(model_results["SVI"]):
            # Update the result with refitted parameters - no JSON serialization needed
            result["tenor_results"] = [model_results["SVI"][i]]
        updated_results.append(result)

    return updated_results


def refit_svi_to_reduce_calendar_arb(
    model_results: dict[str, list[SingleTenorResult]],
) -> None:
    """
    Recalibrates SVI parameters to reduce calendar arbitrage across expiries.

    This function takes in a set of SVI parameters across different expiries,
    constructs total variance curves by strike, applies isotonic regression to
    enforce monotonicity of total variance with respect to time (expiry), and
    then refits SVI parameters per expiry to the smoothed variance surface.

    :param model_results: A dictionary containing the initial 'SVI' fit results.
    """
    svi_list = model_results["SVI"]
    df = pd.DataFrame(svi_list).sort_values(by="expiry").reset_index(drop=True)
    domestic_rates = load_ssm_params(
        {
            "DOMESTIC_RATES": "/data/interest-rates/domestic",
        }
    )["DOMESTIC_RATES"]

    # Step 1: Compute total variance for each expiry and strike
    moneyness_standard = DEFAULT_SURFACE_MONEYNESS
    moneyness_grid = np.tile(moneyness_standard, (len(df), 1))
    f = np.asarray(df["forward"].values, dtype=np.float64)
    forward = f.reshape(-1, 1)
    strike_matrix = forward * moneyness_grid

    # Extract and convert SVI parameters to proper NumPy arrays
    a = np.asarray(df["svi_a"].values, dtype=np.float64)
    b = np.asarray(df["svi_b"].values, dtype=np.float64)
    rho = np.asarray(df["svi_rho"].values, dtype=np.float64)
    m = np.asarray(df["svi_m"].values, dtype=np.float64)
    sigma = np.asarray(df["svi_sigma"].values, dtype=np.float64)

    # Compute vectorized total variance matrix
    tv_matrix = np.asarray(
        svi_var(a=a, b=b, rho=rho, m=m, sigma=sigma, f=f, k=strike_matrix)
    ).astype(np.float64)

    # Step 2: Apply isotonic regression for each strike
    T = np.asarray(df["expiry"].values, dtype=np.float64)
    tv_monotonic = apply_isotonic_regression_by_strike(
        expiries=T,
        total_variance_matrix=tv_matrix,
    )

    # Step 3: Refit SVI per expiry
    def recalibration_condition(sol: dict[str, float]) -> bool:
        return (
            np.isinf(np.asarray(list(sol.values()))).any()
            or np.isnan(np.asarray(list(sol.values()))).any()
            or abs(sol["svi_rho"]) > SABR_RHO_BOUND
        )

    for i, expiry in enumerate(T):
        w_target = tv_monotonic[i]
        row = df.loc[i]
        strike_list = row["forward"] * np.asarray(moneyness_standard)
        vol_target = np.sqrt(w_target / expiry)

        strikes_vol_zip_full_range = list(
            zip(strike_list, vol_target, strict=False)
        )
        r_d = get_domestic_rate(domestic_rates, row["expiry"] * 365)

        query: CalibrationQuery = {
            "expiry": row["expiry"],
            "forward": row["forward"],
            "spot": row["spot"],
            "domestic_rate": r_d,
            "model": "SVI",
            "test_type": "strikes",
            "vol_test_type": "vol_lognormal",
            "LNvols": vol_target,
            "strikes": strike_list,
            "biv": vol_target,
            "aiv": vol_target,
        }

        new_result = perform_svi_calibration(
            query=query,
            method="SLSQP",
            tol=1e-12,
            recalibration_condition=recalibration_condition,
            init_guess_var_by_strike_to_use=strikes_vol_zip_full_range,
            optimizer_options={"maxiter": 1000},
        )

        recalibrated_params = new_result["svi_parameters"]
        a_new = recalibrated_params["svi_a"]
        b_new = recalibrated_params["svi_b"]
        rho_new = recalibrated_params["svi_rho"]
        m_new = recalibrated_params["svi_m"]
        sigma_new = recalibrated_params["svi_sigma"]

        recalibrated_tv = svi_var(
            a=a_new,
            b=b_new,
            rho=rho_new,
            m=m_new,
            sigma=sigma_new,
            f=row["forward"],
            k=strike_list,
        )
        recalibrated_vol = np.sqrt(np.array(recalibrated_tv) / expiry)

        calib_r2 = r2_score(vol_target, recalibrated_vol)

        # Fallback to original params if calibration fails
        if calib_r2 < R2_ACCEPTABLE:
            qualified_name = str(row["qualified_name"])
            timestamp = int(row["timestamp"])
            logging.error(
                f"Low R2 score for {qualified_name=}, {timestamp=}, {expiry=}, {calib_r2=}. Reverting to original params."
            )
            a_new = row["svi_a"]
            b_new = row["svi_b"]
            rho_new = row["svi_rho"]
            m_new = row["svi_m"]
            sigma_new = row["svi_sigma"]

        model_results["SVI"][i] = {
            "svi_a": float(a_new),
            "svi_b": float(b_new),
            "svi_rho": float(rho_new),
            "svi_m": float(m_new),
            "svi_sigma": float(sigma_new),
            "expiry": float(row["expiry"]),
            "timestamp": int(row["timestamp"]),
            "qualified_name": str(row["qualified_name"]),
            "underlying_index": str(row["underlying_index"]),
            "spot": float(row["spot"]),
            "forward": float(row["forward"]),
            "atm_vol": float(row["atm_vol"]),
            "calib_r2": float(calib_r2),
        }
